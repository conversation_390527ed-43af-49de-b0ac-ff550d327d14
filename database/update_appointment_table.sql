-- Update appointment table to add missing columns
-- Run this script in your Supabase SQL editor

-- Add psychometrician_id column
ALTER TABLE appointment
ADD COLUMN IF NOT EXISTS psychometrician_id INTEGER;

-- Add foreign key constraint to psychometricians table
ALTER TABLE appointment
ADD CONSTRAINT fk_appointment_psychometrician
FOREIGN KEY (psychometrician_id) REFERENCES psychometricians(id) ON DELETE CASCADE;

-- Add status column with check constraint
ALTER TABLE appointment
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'scheduled';

ALTER TABLE appointment
ADD CONSTRAINT check_appointment_status
CHECK (status IN ('scheduled', 'completed', 'cancelled', 'no_show', 'rescheduled'));

-- Add notes column
ALTER TABLE appointment
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add consultation_fee column (optional)
ALTER TABLE appointment
ADD COLUMN IF NOT EXISTS consultation_fee DECIMAL(10,2);

-- Add payment_method column (if it doesn't exist)
ALTER TABLE appointment
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointment_psychometrician_id ON appointment(psychometrician_id);
CREATE INDEX IF NOT EXISTS idx_appointment_status ON appointment(status);
CREATE INDEX IF NOT EXISTS idx_appointment_date ON appointment(date);
CREATE INDEX IF NOT EXISTS idx_appointment_user_date ON appointment(user_id, date);

-- Add constraint to ensure end_time is after start_time
ALTER TABLE appointment
ADD CONSTRAINT check_appointment_time
CHECK (end_time > start_time);

-- Add constraint to prevent overlapping appointments for the same psychometrician
-- This ensures no double-booking can occur at the database level
ALTER TABLE appointment
ADD CONSTRAINT no_overlapping_appointments
EXCLUDE USING gist (
    psychometrician_id WITH =,
    date WITH =,
    tsrange(start_time::text::time, end_time::text::time) WITH &&
) WHERE (status IN ('scheduled', 'rescheduled'));
