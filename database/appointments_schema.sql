-- Appointments Table Schema for Supabase
-- This table stores appointment bookings between users and psychometricians

CREATE TABLE IF NOT EXISTS appointments (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL, -- References user table
    psychometrician_id INTEGER NOT NULL REFERENCES psychometricians(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled', 'no_show', 'rescheduled')),
    notes TEXT,
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'refunded', 'failed')),
    payment_method VARCHAR(50),
    consultation_fee DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure end_time is after start_time
    CONSTRAINT valid_appointment_time CHECK (end_time > start_time),
    -- Ensure appointment date is not in the past (can be disabled for testing)
    -- CONSTRAINT future_appointment CHECK (date >= CURRENT_DATE),
    -- Prevent overlapping appointments for the same psychometrician
    EXCLUDE USING gist (
        psychometrician_id WITH =,
        date WITH =,
        tsrange(start_time::text::time, end_time::text::time) WITH &&
    ) WHERE (status IN ('scheduled', 'rescheduled'))
);

-- Add RLS (Row Level Security) policies
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to see only their own appointments
CREATE POLICY "Users can view their own appointments" ON appointments
    FOR SELECT USING (auth.uid()::text = user_id OR auth.role() = 'service_role');

-- Policy to allow users to insert their own appointments
CREATE POLICY "Users can create their own appointments" ON appointments
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- Policy to allow users to update their own appointments
CREATE POLICY "Users can update their own appointments" ON appointments
    FOR UPDATE USING (auth.uid()::text = user_id);

-- Policy to allow users to delete their own appointments (cancel)
CREATE POLICY "Users can cancel their own appointments" ON appointments
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointments_user_id ON appointments(user_id);
CREATE INDEX IF NOT EXISTS idx_appointments_psychometrician_id ON appointments(psychometrician_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments(date);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_datetime ON appointments(date, start_time);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_appointments_updated_at 
    BEFORE UPDATE ON appointments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to check appointment availability
CREATE OR REPLACE FUNCTION check_appointment_availability(
    p_psychometrician_id INTEGER,
    p_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_exclude_appointment_id INTEGER DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if the psychometrician has a schedule for this day
    IF NOT EXISTS (
        SELECT 1 FROM schedules 
        WHERE psychometrician_id = p_psychometrician_id 
        AND day_of_week = LOWER(TO_CHAR(p_date, 'Day'))
        AND is_available = true
        AND p_start_time >= start_time 
        AND p_end_time <= end_time
        -- Check if appointment is not during break time
        AND NOT (
            break_start_time IS NOT NULL 
            AND break_end_time IS NOT NULL
            AND p_start_time < break_end_time 
            AND p_end_time > break_start_time
        )
    ) THEN
        RETURN FALSE;
    END IF;
    
    -- Check for conflicting appointments
    IF EXISTS (
        SELECT 1 FROM appointments 
        WHERE psychometrician_id = p_psychometrician_id
        AND date = p_date
        AND status IN ('scheduled', 'rescheduled')
        AND (id != p_exclude_appointment_id OR p_exclude_appointment_id IS NULL)
        AND (
            (p_start_time >= start_time AND p_start_time < end_time) OR
            (p_end_time > start_time AND p_end_time <= end_time) OR
            (p_start_time <= start_time AND p_end_time >= end_time)
        )
    ) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
