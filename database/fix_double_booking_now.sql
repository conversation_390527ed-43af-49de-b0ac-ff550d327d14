-- IMMEDIATE FIX for double-booking issue
-- Run this script in your Supabase SQL editor RIGHT NOW

-- Step 1: See current overlapping appointments
SELECT 
    'CURRENT CONFLICTS:' as message,
    a1.id as appointment1_id,
    a1.user_id as user1,
    a1.date,
    a1.start_time as start1,
    a1.end_time as end1,
    a2.id as appointment2_id,
    a2.user_id as user2,
    a2.start_time as start2,
    a2.end_time as end2,
    p.name as psychometrician_name
FROM appointment a1
JOIN appointment a2 ON (
    a1.psychometrician_id = a2.psychometrician_id
    AND a1.date = a2.date
    AND a1.id < a2.id
    AND a1.status IN ('scheduled', 'rescheduled')
    AND a2.status IN ('scheduled', 'rescheduled')
    AND (
        (a1.start_time < a2.end_time AND a1.end_time > a2.start_time)
    )
)
JOIN psychometricians p ON a1.psychometrician_id = p.id
ORDER BY a1.date, a1.start_time;

-- Step 2: Cancel the later appointments (keep the earlier ones)
UPDATE appointment 
SET status = 'cancelled', 
    notes = COALESCE(notes, '') || ' [Auto-cancelled due to scheduling conflict - ' || NOW() || ']'
WHERE id IN (
    SELECT a2.id
    FROM appointment a1
    JOIN appointment a2 ON (
        a1.psychometrician_id = a2.psychometrician_id
        AND a1.date = a2.date
        AND a1.id < a2.id  -- Keep the earlier appointment
        AND a1.status IN ('scheduled', 'rescheduled')
        AND a2.status IN ('scheduled', 'rescheduled')
        AND (
            (a1.start_time < a2.end_time AND a1.end_time > a2.start_time)
        )
    )
);

-- Step 3: Add the constraint to prevent future double-booking
-- First drop if exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'no_overlapping_appointments' 
        AND table_name = 'appointment'
    ) THEN
        ALTER TABLE appointment DROP CONSTRAINT no_overlapping_appointments;
    END IF;
END $$;

-- Add the constraint
ALTER TABLE appointment 
ADD CONSTRAINT no_overlapping_appointments 
EXCLUDE USING gist (
    psychometrician_id WITH =,
    date WITH =,
    tsrange(start_time::text::time, end_time::text::time) WITH &&
) WHERE (status IN ('scheduled', 'rescheduled'));

-- Step 4: Verify no more conflicts exist
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'SUCCESS: No more overlapping appointments!'
        ELSE 'ERROR: Still have ' || COUNT(*) || ' overlapping appointments'
    END as result
FROM appointment a1
JOIN appointment a2 ON (
    a1.psychometrician_id = a2.psychometrician_id
    AND a1.date = a2.date
    AND a1.id != a2.id
    AND a1.status IN ('scheduled', 'rescheduled')
    AND a2.status IN ('scheduled', 'rescheduled')
    AND (
        (a1.start_time < a2.end_time AND a1.end_time > a2.start_time)
    )
);
