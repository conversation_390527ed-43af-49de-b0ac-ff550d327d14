-- Schedules Table Schema for Supabase
-- This table stores the weekly schedules for each psychometrician

CREATE TABLE IF NOT EXISTS schedules (
    id SERIAL PRIMARY KEY,
    psychometrician_id INTEGER NOT NULL REFERENCES psychometricians(id) ON DELETE CASCADE,
    day_of_week VARCHAR(10) NOT NULL CHECK (day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    break_start_time TIME,
    break_end_time TIME,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure end_time is after start_time
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    -- Ensure break times are within working hours
    CONSTRAINT valid_break_times CHECK (
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time >= start_time AND break_end_time <= end_time AND break_end_time > break_start_time)
    ),
    -- Prevent duplicate schedules for same psychometrician and day
    UNIQUE(psychometrician_id, day_of_week)
);

-- Add RLS (Row Level Security) policies
ALTER TABLE schedules ENABLE ROW LEVEL SECURITY;

-- Policy to allow all users to read schedules (for appointment booking)
CREATE POLICY "Allow public read access to schedules" ON schedules
    FOR SELECT USING (true);

-- Policy to allow only authenticated users to modify schedules
CREATE POLICY "Allow authenticated users to modify schedules" ON schedules
    FOR ALL USING (auth.role() = 'authenticated');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schedules_psychometrician_id ON schedules(psychometrician_id);
CREATE INDEX IF NOT EXISTS idx_schedules_day_of_week ON schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_schedules_available ON schedules(is_available);
CREATE INDEX IF NOT EXISTS idx_schedules_time_range ON schedules(start_time, end_time);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_schedules_updated_at
    BEFORE UPDATE ON schedules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample Data for Schedules Table
-- Insert sample schedules for the psychometricians

INSERT INTO schedules (psychometrician_id, day_of_week, start_time, end_time, break_start_time, break_end_time, notes) VALUES
-- Dr. Sarah Johnson (ID: 1) - Monday to Friday, 9 AM to 5 PM
(1, 'monday', '09:00', '17:00', '12:00', '13:00', 'Regular office hours'),
(1, 'tuesday', '09:00', '17:00', '12:00', '13:00', 'Regular office hours'),
(1, 'wednesday', '09:00', '17:00', '12:00', '13:00', 'Regular office hours'),
(1, 'thursday', '09:00', '17:00', '12:00', '13:00', 'Regular office hours'),
(1, 'friday', '09:00', '17:00', '12:00', '13:00', 'Regular office hours'),

-- Dr. Michael Chen (ID: 2) - Monday to Friday, 10 AM to 6 PM
(2, 'monday', '10:00', '18:00', '13:00', '14:00', 'Neuropsychology assessments'),
(2, 'tuesday', '10:00', '18:00', '13:00', '14:00', 'Neuropsychology assessments'),
(2, 'wednesday', '10:00', '18:00', '13:00', '14:00', 'Neuropsychology assessments'),
(2, 'thursday', '10:00', '18:00', '13:00', '14:00', 'Neuropsychology assessments'),
(2, 'friday', '10:00', '18:00', '13:00', '14:00', 'Neuropsychology assessments'),

-- Dr. Emily Rodriguez (ID: 3) - Monday, Wednesday, Friday, 8 AM to 4 PM
(3, 'monday', '08:00', '16:00', '12:00', '13:00', 'Trauma therapy sessions'),
(3, 'wednesday', '08:00', '16:00', '12:00', '13:00', 'Trauma therapy sessions'),
(3, 'friday', '08:00', '16:00', '12:00', '13:00', 'Trauma therapy sessions'),

-- Dr. James Wilson (ID: 4) - Tuesday to Saturday, 11 AM to 7 PM
(4, 'tuesday', '11:00', '19:00', '14:00', '15:00', 'CBT sessions'),
(4, 'wednesday', '11:00', '19:00', '14:00', '15:00', 'CBT sessions'),
(4, 'thursday', '11:00', '19:00', '14:00', '15:00', 'CBT sessions'),
(4, 'friday', '11:00', '19:00', '14:00', '15:00', 'CBT sessions'),
(4, 'saturday', '11:00', '19:00', '14:00', '15:00', 'CBT sessions'),

-- Dr. Lisa Park (ID: 5) - Monday to Friday, 2 PM to 8 PM (after school hours)
(5, 'monday', '14:00', '20:00', '17:00', '18:00', 'Child and adolescent sessions'),
(5, 'tuesday', '14:00', '20:00', '17:00', '18:00', 'Child and adolescent sessions'),
(5, 'wednesday', '14:00', '20:00', '17:00', '18:00', 'Child and adolescent sessions'),
(5, 'thursday', '14:00', '20:00', '17:00', '18:00', 'Child and adolescent sessions'),
(5, 'friday', '14:00', '20:00', '17:00', '18:00', 'Child and adolescent sessions'),

-- Dr. Robert Martinez (ID: 6) - Monday to Friday, 9 AM to 5 PM
(6, 'monday', '09:00', '17:00', '12:30', '13:30', 'Substance abuse counseling'),
(6, 'tuesday', '09:00', '17:00', '12:30', '13:30', 'Substance abuse counseling'),
(6, 'wednesday', '09:00', '17:00', '12:30', '13:30', 'Substance abuse counseling'),
(6, 'thursday', '09:00', '17:00', '12:30', '13:30', 'Substance abuse counseling'),
(6, 'friday', '09:00', '17:00', '12:30', '13:30', 'Substance abuse counseling'),

-- Dr. David Kim (ID: 8) - Monday, Wednesday, Friday, 9 AM to 3 PM
(8, 'monday', '09:00', '15:00', '12:00', '13:00', 'Geriatric psychology'),
(8, 'wednesday', '09:00', '15:00', '12:00', '13:00', 'Geriatric psychology'),
(8, 'friday', '09:00', '15:00', '12:00', '13:00', 'Geriatric psychology');
