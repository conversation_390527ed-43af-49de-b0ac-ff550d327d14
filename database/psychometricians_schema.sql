-- Psychometricians Table Schema for Supabase
-- This table stores information about psychometricians available for appointments

CREATE TABLE IF NOT EXISTS psychometricians (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    specialty VARCHAR(255),
    experience VARCHAR(100),
    rating DECIMAL(2,1) DEFAULT 4.5,
    image VARCHAR(10) DEFAULT '👨‍⚕️',
    available BOOLEAN DEFAULT true,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    license_number VARCHAR(100),
    education TEXT,
    bio TEXT,
    consultation_fee DECIMAL(10,2) DEFAULT 120.00,
    languages TEXT[] DEFAULT ARRAY['English'],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS (Row Level Security) policies
ALTER TABLE psychometricians ENABLE ROW LEVEL SECURITY;

-- Policy to allow all users to read psychometricians (for appointment booking)
CREATE POLICY "Allow public read access to psychometricians" ON psychometricians
    FOR SELECT USING (true);

-- Policy to allow only authenticated users to see contact details
CREATE POLICY "Allow authenticated users full access" ON psychometricians
    FOR ALL USING (auth.role() = 'authenticated');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_psychometricians_available ON psychometricians(available);
CREATE INDEX IF NOT EXISTS idx_psychometricians_specialty ON psychometricians(specialty);
CREATE INDEX IF NOT EXISTS idx_psychometricians_rating ON psychometricians(rating DESC);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_psychometricians_updated_at 
    BEFORE UPDATE ON psychometricians 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
