-- Enable double-booking prevention constraint
-- Run this script in your Supabase SQL editor to prevent overlapping appointments

-- IMPORTANT: This script will clean up existing overlapping appointments first
-- Make sure to backup your data before running this script

-- First, let's see what overlapping appointments exist
DO $$
DECLARE
    overlap_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO overlap_count
    FROM appointment a1
    JOIN appointment a2 ON (
        a1.psychometrician_id = a2.psychometrician_id
        AND a1.date = a2.date
        AND a1.id != a2.id
        AND a1.status IN ('scheduled', 'rescheduled')
        AND a2.status IN ('scheduled', 'rescheduled')
        AND (
            (a1.start_time >= a2.start_time AND a1.start_time < a2.end_time) OR
            (a1.end_time > a2.start_time AND a1.end_time <= a2.end_time) OR
            (a1.start_time <= a2.start_time AND a1.end_time >= a2.end_time)
        )
    );

    RAISE NOTICE 'Found % overlapping appointments that need to be resolved', overlap_count;
END $$;

-- Check if the constraint already exists and drop it if it does
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'no_overlapping_appointments'
        AND table_name = 'appointment'
    ) THEN
        ALTER TABLE appointment DROP CONSTRAINT no_overlapping_appointments;
        RAISE NOTICE 'Dropped existing no_overlapping_appointments constraint';
    END IF;
END $$;

-- OPTION 1: Cancel duplicate appointments (keeps the first one, cancels the rest)
-- Uncomment this section if you want to automatically cancel overlapping appointments
/*
UPDATE appointment
SET status = 'cancelled',
    notes = COALESCE(notes, '') || ' [Auto-cancelled due to scheduling conflict]'
WHERE id IN (
    SELECT a2.id
    FROM appointment a1
    JOIN appointment a2 ON (
        a1.psychometrician_id = a2.psychometrician_id
        AND a1.date = a2.date
        AND a1.id < a2.id  -- Keep the earlier appointment
        AND a1.status IN ('scheduled', 'rescheduled')
        AND a2.status IN ('scheduled', 'rescheduled')
        AND (
            (a1.start_time >= a2.start_time AND a1.start_time < a2.end_time) OR
            (a1.end_time > a2.start_time AND a1.end_time <= a2.end_time) OR
            (a1.start_time <= a2.start_time AND a1.end_time >= a2.end_time)
        )
    )
);
*/

-- OPTION 2: Manual resolution required
-- If you have overlapping appointments, you need to manually resolve them first
-- You can run this query to see the conflicts:
/*
SELECT
    a1.id as appointment1_id,
    a1.user_id as user1,
    a1.date,
    a1.start_time as start1,
    a1.end_time as end1,
    a2.id as appointment2_id,
    a2.user_id as user2,
    a2.start_time as start2,
    a2.end_time as end2,
    p.name as psychometrician_name
FROM appointment a1
JOIN appointment a2 ON (
    a1.psychometrician_id = a2.psychometrician_id
    AND a1.date = a2.date
    AND a1.id < a2.id
    AND a1.status IN ('scheduled', 'rescheduled')
    AND a2.status IN ('scheduled', 'rescheduled')
    AND (
        (a1.start_time >= a2.start_time AND a1.start_time < a2.end_time) OR
        (a1.end_time > a2.start_time AND a1.end_time <= a2.end_time) OR
        (a1.start_time <= a2.start_time AND a1.end_time >= a2.end_time)
    )
)
JOIN psychometricians p ON a1.psychometrician_id = p.id
ORDER BY a1.date, a1.start_time;
*/

-- Add the constraint to prevent overlapping appointments for the same psychometrician
-- This ensures no double-booking can occur at the database level
-- NOTE: This will fail if there are existing overlapping appointments
ALTER TABLE appointment
ADD CONSTRAINT no_overlapping_appointments
EXCLUDE USING gist (
    psychometrician_id WITH =,
    date WITH =,
    tsrange(start_time::text::time, end_time::text::time) WITH &&
) WHERE (status IN ('scheduled', 'rescheduled'));

-- Create a function to check appointment conflicts before insertion
CREATE OR REPLACE FUNCTION check_appointment_conflict()
RETURNS TRIGGER AS $$
BEGIN
    -- Check for overlapping appointments
    IF EXISTS (
        SELECT 1 FROM appointment
        WHERE psychometrician_id = NEW.psychometrician_id
        AND date = NEW.date
        AND status IN ('scheduled', 'rescheduled')
        AND id != COALESCE(NEW.id, -1)  -- Exclude current appointment for updates
        AND (
            (NEW.start_time >= start_time AND NEW.start_time < end_time) OR
            (NEW.end_time > start_time AND NEW.end_time <= end_time) OR
            (NEW.start_time <= start_time AND NEW.end_time >= end_time)
        )
    ) THEN
        RAISE EXCEPTION 'This time slot is already booked for this psychometrician. Please select a different time.';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to check for conflicts on insert and update
DROP TRIGGER IF EXISTS check_appointment_conflict_trigger ON appointment;
CREATE TRIGGER check_appointment_conflict_trigger
    BEFORE INSERT OR UPDATE ON appointment
    FOR EACH ROW
    EXECUTE FUNCTION check_appointment_conflict();

-- Add helpful comments
COMMENT ON CONSTRAINT no_overlapping_appointments ON appointment IS 'Prevents double-booking by ensuring no overlapping time slots for the same psychometrician';
COMMENT ON FUNCTION check_appointment_conflict() IS 'Checks for appointment conflicts and provides user-friendly error messages';
COMMENT ON TRIGGER check_appointment_conflict_trigger ON appointment IS 'Automatically prevents appointment conflicts on insert/update operations';
