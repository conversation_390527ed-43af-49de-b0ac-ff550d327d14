import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Switch
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import CustomStatusBar from './components/CustomStatusBar';

const AppSettings = () => {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Settings state
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);
  const [dataSharing, setDataSharing] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('English');

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const securitySettings = [
    {
      id: 'change-password',
      title: 'Change Password',
      onPress: () => router.push('/change-password'),
      showArrow: true
    },
    {
      id: 'two-factor',
      title: 'Two-Factor Authentication',
      value: twoFactorAuth,
      onToggle: setTwoFactorAuth,
      isToggle: true
    },
    {
      id: 'data-sharing',
      title: 'Data Sharing',
      value: dataSharing,
      onToggle: setDataSharing,
      isToggle: true
    }
  ];

  const appSharingSettings = [
    {
      id: 'notifications',
      title: 'Notifications',
      value: notifications,
      onToggle: setNotifications,
      isToggle: true
    },
    {
      id: 'dark-mode',
      title: 'Dark mode',
      value: darkMode,
      onToggle: setDarkMode,
      isToggle: true
    },
    {
      id: 'language',
      title: 'Language',
      value: language,
      onPress: () => router.push('/language-settings'),
      showArrow: true
    }
  ];

  const renderSettingItem = (item) => (
    <View key={item.id} style={styles.settingItem}>
      <Text style={styles.settingTitle}>{item.title}</Text>
      {item.isToggle ? (
        <Switch
          value={item.value}
          onValueChange={item.onToggle}
          trackColor={{ false: '#E8E9ED', true: '#7BA05B' }}
          thumbColor={item.value ? '#FFFFFF' : '#FFFFFF'}
          ios_backgroundColor="#E8E9ED"
          style={{ transform: [{ scaleX: 1.1 }, { scaleY: 1.1 }] }}
        />
      ) : (
        <TouchableOpacity onPress={item.onPress} style={styles.settingAction}>
          {item.value && typeof item.value === 'string' && (
            <Text style={styles.settingValue}>{item.value}</Text>
          )}
          {item.showArrow && (
            <Text style={styles.settingArrow}>›</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Text style={styles.backButtonIcon}>←</Text>
              <Text style={styles.backButtonText}>Back</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>App settings</Text>
            <View style={styles.headerSpacer} />
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Security Section */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.sectionTitle}>Security</Text>
            <View style={styles.sectionContent}>
              {securitySettings.map(renderSettingItem)}
            </View>
          </Animated.View>

          {/* App Sharing Section */}
          <Animated.View
            style={[
              styles.section,
              { opacity: fadeAnim }
            ]}
          >
            <Text style={styles.sectionTitle}>App Sharing</Text>
            <View style={styles.sectionContent}>
              {appSharingSettings.map(renderSettingItem)}
            </View>
          </Animated.View>

          {/* Version Info */}
          <View style={styles.versionSection}>
            <Text style={styles.versionText}>MentalEase v1.0.0</Text>
            <Text style={styles.copyrightText}>© 2023 MentalEase Inc.</Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );
};

export default AppSettings;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 60 : StatusBar.currentHeight + 30,
    paddingBottom: 25,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  backButtonIcon: {
    fontSize: 22,
    color: '#FFFFFF',
    marginRight: 6,
  },
  backButtonText: {
    fontSize: 17,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  headerSpacer: {
    width: 70,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 120,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 18,
    letterSpacing: 0.5,
  },
  sectionContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 20,
    paddingHorizontal: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F6FA',
  },
  settingTitle: {
    fontSize: 17,
    color: '#2C3E50',
    fontWeight: '600',
    flex: 1,
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: 16,
    color: '#7F8C8D',
    marginRight: 12,
    fontWeight: '500',
  },
  settingArrow: {
    fontSize: 22,
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  versionSection: {
    alignItems: 'center',
    marginTop: 50,
  },
  versionText: {
    fontSize: 15,
    color: '#95A5A6',
    marginBottom: 6,
    fontWeight: '500',
  },
  copyrightText: {
    fontSize: 15,
    color: '#95A5A6',
    fontWeight: '500',
  },
});
