import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image,
  Dimensions,
  Modal,
  Easing
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const { width } = Dimensions.get('window');

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const modalSlideAnim = useRef(new Animated.Value(300)).current;
  const modalOpacityAnim = useRef(new Animated.Value(0)).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showDailyInsights, setShowDailyInsights] = useState(true);
  const [showInsightsModal, setShowInsightsModal] = useState(false);

  // Mood options matching the design from images
  const moodOptions = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😐', label: 'Meh' },
    { emoji: '🙂', label: 'Okay' },
    { emoji: '😊', label: 'Good' },
    { emoji: '😄', label: 'Great' }
  ];

  // Sample appointment data
  const upcomingAppointment = {
    doctor: 'Dr. Abie Rufino',
    specialty: 'Psychometrician',
    date: 'May 25, 2025',
    time: '8:00 am',
    initial: 'A'
  };

  useEffect(() => {
    // Enhanced entrance animations with staggered effect
    Animated.stagger(200, [
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(slideUpAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Enhanced notification pulse animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 1500,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.ease),
        }),
      ])
    ).start();
  }, []);

  // Mood selection animation
  const moodScaleAnims = moodOptions.map(() => useRef(new Animated.Value(1)).current);

  const animateMoodSelection = (index) => {
    // Enhanced mood selection animation with haptic feedback
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 150,
        useNativeDriver: true,
        easing: Easing.out(Easing.cubic),
      }),
      Animated.spring(scaleAnim, {
        toValue: 1.1,
        tension: 300,
        friction: 6,
        useNativeDriver: true,
      })
    ]).start();
  };

  const handleMoodSelect = (index) => {
    setSelectedMood(index);
    animateMoodSelection(index);

    // Add subtle haptic feedback for better UX
    // Note: You can add Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light) here
  };

  const handleSaveMood = () => {
    if (selectedMood !== null) {
      router.push({
        pathname: '/mood-journal',
        params: {
          mood: moodOptions[selectedMood].emoji,
          moodIndex: selectedMood
        }
      });
    }
  };

  const handleProfilePress = () => {
    router.push('/user-profile-redesigned');
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Daily Insights Modal Animation
  const showInsightsModalWithAnimation = () => {
    setShowInsightsModal(true);
    Animated.parallel([
      Animated.timing(modalOpacityAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(modalSlideAnim, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      })
    ]).start();
  };

  const hideInsightsModalWithAnimation = () => {
    Animated.parallel([
      Animated.timing(modalOpacityAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(modalSlideAnim, {
        toValue: 300,
        duration: 200,
        useNativeDriver: true,
      })
    ]).start(() => {
      setShowInsightsModal(false);
      setShowDailyInsights(false);
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar
        translucent={true}
        backgroundColor="transparent"
        barStyle="light-content"
      />

      <SafeAreaView style={styles.safeArea}>
        {/* Curved Header with Gradient - Redesigned to match first image */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfoContainer}>
              <TouchableOpacity
                style={styles.profilePicture}
                onPress={handleProfilePress}
              >
                {userData.profileImage ? (
                  <Image source={userData.profileImage} style={styles.profileImage} />
                ) : (
                  <Text style={styles.profileInitial}>
                    {(userData.firstName || 'L').charAt(0)}
                  </Text>
                )}
              </TouchableOpacity>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>
                  {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
                </Text>
              </View>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <View style={styles.notificationIcon}>
                <View style={styles.notificationIconContainer}>
                  <View style={styles.notificationBell}>
                    <Text style={styles.notificationText}>🔔</Text>
                    <Animated.View
                      style={[
                        styles.notificationBadge,
                        { transform: [{ scale: pulseAnim }] }
                      ]}
                    />
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Mood Tracker Card - Redesigned to match images */}
          <Animated.View
            style={[
              styles.moodCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.moodCardContent}>
              <Text style={styles.moodTitle}>How are you feeling today?</Text>
              <View style={styles.moodDateContainer}>
                <Text style={styles.moodSubtitle}>Today's mood</Text>
                <Text style={styles.moodDate}>{getCurrentDate()}</Text>
              </View>

              <View style={styles.moodOptionsContainer}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.moodOptionWrapper}
                    onPress={() => handleMoodSelect(index)}
                    activeOpacity={0.7}
                  >
                    <Animated.View style={[
                      styles.moodEmojiContainer,
                      selectedMood === index && styles.selectedMoodEmojiContainer,
                      {
                        transform: [{
                          scale: selectedMood === index ? scaleAnim : 1
                        }]
                      }
                    ]}>
                      <Text style={[
                        styles.moodEmoji,
                        selectedMood === index && styles.selectedMoodEmoji
                      ]}>
                        {mood.emoji}
                      </Text>
                    </Animated.View>
                    <Text style={[
                      styles.moodLabel,
                      selectedMood === index && styles.selectedMoodLabel
                    ]}>
                      {mood.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity
                style={[
                  styles.saveMoodButton,
                  selectedMood === null && styles.saveMoodButtonDisabled
                ]}
                onPress={handleSaveMood}
                disabled={selectedMood === null}
                activeOpacity={0.8}
              >
                <Text style={styles.saveMoodButtonText}>Save Mood</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Daily Insights Card - Redesigned to match first image */}
          {showDailyInsights && (
            <Animated.View
              style={[
                styles.insightsCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <TouchableOpacity
                style={styles.insightsCardTouchable}
                onPress={showInsightsModalWithAnimation}
                activeOpacity={0.95}
              >
                <View style={styles.insightsHeader}>
                  <Text style={styles.insightsTitle}>Daily Insights</Text>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={() => setShowDailyInsights(false)}
                  >
                    <Text style={styles.closeButtonText}>✕</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.insightsPreview}>
                  <View style={styles.insightsAccent} />
                  <Text style={styles.insightsText}>
                    It seems like you are stress in the past few weeks.{'\n'}
                    Here are some recommendations:
                  </Text>
                </View>
                <View style={styles.recommendationsList}>
                  <Text style={styles.recommendationItem}>• Take a deept breath</Text>
                  <Text style={styles.recommendationItem}>• Write down what is bothering you</Text>
                  <Text style={styles.recommendationItem}>• Do something kind to yourself</Text>
                </View>
              </TouchableOpacity>
            </Animated.View>
          )}

          {/* Upcoming Appointment Card - Redesigned */}
          <Animated.View
            style={[
              styles.appointmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <Text style={styles.appointmentTitle}>Upcoming Appointment</Text>
            <View style={styles.appointmentContent}>
              <View style={styles.doctorInfo}>
                <View style={styles.doctorAvatar}>
                  <Text style={styles.doctorInitial}>{upcomingAppointment.initial}</Text>
                </View>
                <View style={styles.doctorDetails}>
                  <Text style={styles.doctorName}>{upcomingAppointment.doctor}</Text>
                  <Text style={styles.doctorSpecialty}>{upcomingAppointment.specialty}</Text>
                </View>
              </View>
              <View style={styles.appointmentTime}>
                <Text style={styles.appointmentDate}>{upcomingAppointment.date}</Text>
                <Text style={styles.appointmentTimeText}>{upcomingAppointment.time}</Text>
              </View>
            </View>
            <TouchableOpacity
              style={styles.rescheduleButton}
              onPress={() => router.push('/reschedule-appointment')}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* AI Mental Health Assistant Card - Redesigned */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mental Health Assessment Card */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#7BA05B', '#9BC76D']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
              <Text style={styles.assessmentDescription}>
                Take a quuick assessment to better understand your current mental health state and get personalized recommendation
              </Text>
              <TouchableOpacity
                style={styles.startAssessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.startAssessmentButtonText}>Start self-assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Stress Management Card */}
          <Animated.View
            style={[
              styles.stressManagementCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.stressManagementContent}>
              <View style={styles.stressManagementHeader}>
                <View style={styles.stressIcon}>
                  <Text style={styles.stressIconText}>🧘</Text>
                </View>
                <Text style={styles.stressManagementTitle}>Stress Management</Text>
              </View>
              <Text style={styles.stressManagementDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.startJournalingButton}
                onPress={() => router.push('/mood-journal')}
              >
                <Text style={styles.startJournalingButtonText}>Start journaling</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Daily Insights Modal - Matching the design from first image */}
        <Modal
          visible={showInsightsModal}
          transparent={true}
          animationType="none"
          onRequestClose={hideInsightsModalWithAnimation}
        >
          <Animated.View
            style={[
              styles.modalOverlay,
              { opacity: modalOpacityAnim }
            ]}
          >
            <TouchableOpacity
              style={styles.modalBackdrop}
              activeOpacity={1}
              onPress={hideInsightsModalWithAnimation}
            />
            <Animated.View
              style={[
                styles.insightsModal,
                {
                  transform: [{ translateY: modalSlideAnim }]
                }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Daily Insights</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={hideInsightsModalWithAnimation}
                >
                  <Text style={styles.modalCloseText}>✕</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.modalContent}>
                <View style={styles.modalInsightsAccent} />
                <Text style={styles.modalInsightsText}>
                  It seems like you are stress in the past few weeks.{'\n'}
                  Here are some recommendations:
                </Text>
                <View style={styles.modalRecommendationsList}>
                  <Text style={styles.modalRecommendationItem}>• Take a deept breath</Text>
                  <Text style={styles.modalRecommendationItem}>• Write down what is bothering you</Text>
                  <Text style={styles.modalRecommendationItem}>• Do something kind to yourself</Text>
                </View>
              </View>
            </Animated.View>
          </Animated.View>
        </Modal>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 60 : StatusBar.currentHeight + 30,
    paddingBottom: 50,
    paddingHorizontal: 24,
    borderBottomLeftRadius: 40,
    borderBottomRightRadius: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 10,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  profileImage: {
    width: 52,
    height: 52,
    borderRadius: 26,
  },
  profileInitial: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  notificationButton: {
    padding: 8,
  },
  notificationIcon: {
    position: 'relative',
  },
  notificationIconContainer: {
    position: 'relative',
  },
  notificationBell: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  notificationText: {
    fontSize: 22,
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FF4757',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 120,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 15,
    elevation: 8,
    marginTop: -30,
  },
  moodCardContent: {
    padding: 24,
  },
  moodTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 8,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  moodSubtitle: {
    fontSize: 15,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  moodDate: {
    fontSize: 15,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  moodOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 28,
    paddingHorizontal: 8,
  },
  moodOptionWrapper: {
    alignItems: 'center',
    flex: 1,
  },
  moodEmojiContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedMoodEmojiContainer: {
    backgroundColor: '#7BA05B',
    borderColor: '#7BA05B',
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  moodEmoji: {
    fontSize: 28,
  },
  selectedMoodEmoji: {
    fontSize: 30,
  },
  moodLabel: {
    fontSize: 13,
    color: '#7F8C8D',
    fontWeight: '600',
    textAlign: 'center',
  },
  selectedMoodLabel: {
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 28,
    paddingVertical: 16,
    alignItems: 'center',
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  saveMoodButtonDisabled: {
    backgroundColor: '#E0E0E0',
    shadowOpacity: 0,
    elevation: 0,
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  insightsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  insightsCardTouchable: {
    padding: 24,
  },
  insightsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  insightsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  closeButton: {
    padding: 8,
    borderRadius: 15,
    backgroundColor: '#F8F9FA',
  },
  closeButtonText: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  insightsPreview: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  insightsAccent: {
    width: 4,
    height: 40,
    backgroundColor: '#7BA05B',
    borderRadius: 2,
    marginRight: 16,
  },
  insightsText: {
    fontSize: 15,
    color: '#5D6D7E',
    lineHeight: 22,
    flex: 1,
    fontStyle: 'italic',
  },
  recommendationsList: {
    paddingLeft: 20,
  },
  recommendationItem: {
    fontSize: 15,
    color: '#5D6D7E',
    lineHeight: 24,
    marginBottom: 6,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    marginBottom: 24,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  appointmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 18,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 22,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  doctorInitial: {
    fontSize: 22,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  doctorSpecialty: {
    fontSize: 15,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 15,
    color: '#2C3E50',
    fontWeight: '600',
    marginBottom: 4,
  },
  appointmentTimeText: {
    fontSize: 15,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  rescheduleButton: {
    backgroundColor: '#9BC76D',
    borderRadius: 28,
    paddingVertical: 14,
    alignItems: 'center',
    shadowColor: '#9BC76D',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  aiAssistantCard: {
    backgroundColor: '#E8F5E8',
    borderRadius: 25,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  aiAssistantContent: {
    padding: 24,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  aiIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  aiIconText: {
    fontSize: 22,
  },
  aiAssistantTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  aiAssistantSubtitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 10,
    lineHeight: 24,
  },
  aiAssistantDescription: {
    fontSize: 15,
    color: '#5D6D7E',
    lineHeight: 22,
    marginBottom: 22,
  },
  chatButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 28,
    paddingVertical: 14,
    alignItems: 'center',
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  assessmentCard: {
    borderRadius: 25,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 8,
  },
  assessmentGradient: {
    padding: 24,
    borderRadius: 25,
  },
  assessmentTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 14,
    letterSpacing: 0.5,
  },
  assessmentDescription: {
    fontSize: 16,
    color: '#FFFFFF',
    lineHeight: 24,
    marginBottom: 24,
    opacity: 0.95,
  },
  startAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 28,
    paddingVertical: 14,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  startAssessmentButtonText: {
    color: '#7BA05B',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  stressManagementCard: {
    backgroundColor: '#F0F8F0',
    borderRadius: 25,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  stressManagementContent: {
    padding: 24,
  },
  stressManagementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 14,
  },
  stressIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stressIconText: {
    fontSize: 22,
  },
  stressManagementTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  stressManagementDescription: {
    fontSize: 15,
    color: '#5D6D7E',
    lineHeight: 22,
    marginBottom: 22,
  },
  startJournalingButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 28,
    paddingVertical: 14,
    alignItems: 'center',
    shadowColor: '#7BA05B',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  startJournalingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  modalBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  insightsModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 15,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#2C3E50',
  },
  modalCloseButton: {
    padding: 8,
    borderRadius: 15,
    backgroundColor: '#F8F9FA',
  },
  modalCloseText: {
    fontSize: 16,
    color: '#7F8C8D',
    fontWeight: '600',
  },
  modalContent: {
    padding: 24,
  },
  modalInsightsAccent: {
    width: 4,
    height: 50,
    backgroundColor: '#7BA05B',
    borderRadius: 2,
    marginBottom: 16,
  },
  modalInsightsText: {
    fontSize: 16,
    color: '#5D6D7E',
    lineHeight: 24,
    marginBottom: 20,
    fontStyle: 'italic',
  },
  modalRecommendationsList: {
    paddingLeft: 0,
  },
  modalRecommendationItem: {
    fontSize: 16,
    color: '#5D6D7E',
    lineHeight: 26,
    marginBottom: 8,
  },
});

export default Dashboard;
