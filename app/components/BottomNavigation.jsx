import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated, Platform } from 'react-native';
import { useRouter, usePathname } from 'expo-router';

const BottomNavigation = () => {
  const router = useRouter();
  const pathname = usePathname();

  // Animation value for the active indicator
  const [activeIndicator] = React.useState(new Animated.Value(0));

  // Standardize navigation method across the app
  const navigateTo = (path) => {
    // Only navigate if we're not already on this path
    if (pathname !== path) {
      // Use replace for instant navigation without animation
      router.replace(path);
    }
  };

  // Define route groups for better organization
  const routeGroups = {
    home: ['/dashboard'],
    journal: ['/mood-journal', '/mood-tracker'],
    chatbot: ['/ai-chatbot'],
    appointments: [
      '/appointments',
      '/schedule-appointment',
      '/reschedule-appointment',
      '/online-consultation'
    ],
    assessment: [
      '/mental-assessment',
      '/assessment-questions',
      '/assessment-results'
    ]
  };

  // Check which group the current path belongs to
  const isInGroup = (group) => routeGroups[group].includes(pathname);

  // Get the active tab index for animation
  const getActiveTabIndex = () => {
    if (isInGroup('home')) return 0;
    if (isInGroup('journal')) return 1;
    if (isInGroup('chatbot')) return 2;
    if (isInGroup('appointments')) return 3;
    if (isInGroup('assessment')) return 4;
    return 2; // Default to center (chatbot)
  };

  // Animate the indicator when the active tab changes
  useEffect(() => {
    Animated.spring(activeIndicator, {
      toValue: getActiveTabIndex(),
      useNativeDriver: false,
      friction: 8,
      tension: 50
    }).start();
  }, [pathname]);

  return (
    <View style={styles.bottomNavContainer}>
      {/* Active Tab Indicator */}
      <Animated.View
        style={[
          styles.activeIndicator,
          {
            left: activeIndicator.interpolate({
              inputRange: [0, 1, 2, 3, 4],
              outputRange: ['10%', '30%', '50%', '70%', '90%']
            })
          }
        ]}
      />

      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={[styles.navItem, isInGroup('home') && styles.activeNavItem]}
          onPress={() => navigateTo('/dashboard')}
          activeOpacity={0.7}
        >
          <View style={[
            styles.navIconContainer,
            isInGroup('home') && styles.activeIconContainer
          ]}>
            <Text style={styles.navIcon}>🏠</Text>
          </View>
          <Text style={[
            styles.navText,
            isInGroup('home') && styles.activeNavText
          ]}>Home</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navItem, isInGroup('journal') && styles.activeNavItem]}
          onPress={() => navigateTo('/mood-journal')}
          activeOpacity={0.7}
        >
          <View style={[
            styles.navIconContainer,
            isInGroup('journal') && styles.activeIconContainer
          ]}>
            <Text style={styles.navIcon}>📝</Text>
          </View>
          <Text style={[
            styles.navText,
            isInGroup('journal') && styles.activeNavText
          ]}>Journal</Text>
        </TouchableOpacity>

        {/* Center Chat Button */}
        <TouchableOpacity
          style={styles.centerButton}
          onPress={() => navigateTo('/ai-chatbot')}
          activeOpacity={0.85}
        >
          <View style={[
            styles.centerButtonInner,
            isInGroup('chatbot') && styles.activeCenterButton
          ]}>
            <Text style={styles.centerButtonIcon}>💬</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navItem, isInGroup('appointments') && styles.activeNavItem]}
          onPress={() => navigateTo('/appointments')}
          activeOpacity={0.7}
        >
          <View style={[
            styles.navIconContainer,
            isInGroup('appointments') && styles.activeIconContainer
          ]}>
            <Text style={styles.navIcon}>📅</Text>
          </View>
          <Text style={[
            styles.navText,
            isInGroup('appointments') && styles.activeNavText
          ]}>Appoint</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.navItem, isInGroup('assessment') && styles.activeNavItem]}
          onPress={() => navigateTo('/mental-assessment')}
          activeOpacity={0.7}
        >
          <View style={[
            styles.navIconContainer,
            isInGroup('assessment') && styles.activeIconContainer
          ]}>
            <Text style={styles.navIcon}>🧠</Text>
          </View>
          <Text style={[
            styles.navText,
            isInGroup('assessment') && styles.activeNavText
          ]}>Assess</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomNavContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  bottomNav: {
    height: 75,
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 10,
    paddingHorizontal: 10,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingBottom: Platform.OS === 'ios' ? 20 : 10,
  },
  activeIndicator: {
    position: 'absolute',
    top: -3,
    width: '4%',
    height: 3,
    backgroundColor: '#7BA05B',
    borderRadius: 3,
    zIndex: 10,
    transform: [{ translateX: -10 }]
  },
  navItem: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  activeNavItem: {
    transform: [{ translateY: -3 }],
  },
  navIconContainer: {
    width: 42,
    height: 42,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    backgroundColor: '#F8F8F8',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 4,
  },
  activeIconContainer: {
    backgroundColor: '#F5F9EE',
    borderWidth: 1,
    borderColor: '#7BA05B',
  },
  navIcon: {
    fontSize: 22,
  },
  navText: {
    fontSize: 11,
    color: '#666666',
    fontWeight: '500',
  },
  activeNavText: {
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  centerButton: {
    width: 68,
    height: 68,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -34,
  },
  centerButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#7BA05B',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 8,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  activeCenterButton: {
    backgroundColor: '#6B9142',
    transform: [{ scale: 1.05 }],
  },
  centerButtonIcon: {
    fontSize: 26,
    color: '#FFFFFF',
  },
});

export default BottomNavigation;
