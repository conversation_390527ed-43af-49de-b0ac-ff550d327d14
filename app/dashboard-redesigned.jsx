import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Animated,
  Platform,
  StatusBar,
  Image
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import BottomNavigation from './components/BottomNavigation';
import CustomStatusBar from './components/CustomStatusBar';
import { useUser } from './context/UserContext';

const Dashboard = () => {
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideUpAnim = useRef(new Animated.Value(50)).current;

  const router = useRouter();
  const { userData, toggleHidePersonalInfo } = useUser();

  // State management
  const [selectedMood, setSelectedMood] = useState(null);
  const [showDailyInsights, setShowDailyInsights] = useState(true);

  // Mood options matching the design
  const moodOptions = [
    { emoji: '😔', label: 'Sad' },
    { emoji: '😐', label: 'Meh' },
    { emoji: '🙂', label: 'Okay' },
    { emoji: '😊', label: 'Good' },
    { emoji: '😄', label: 'Great' }
  ];

  // Sample appointment data
  const upcomingAppointment = {
    doctor: 'Dr. Abie Rufino',
    specialty: 'Psychometrician',
    date: 'May 25, 2025',
    time: '8:00 am',
    initial: 'A'
  };

  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleMoodSelect = (index) => {
    setSelectedMood(index);
  };

  const handleSaveMood = () => {
    if (selectedMood !== null) {
      router.push({
        pathname: '/mood-journal',
        params: {
          mood: moodOptions[selectedMood].emoji,
          moodIndex: selectedMood
        }
      });
    }
  };

  const handleProfilePress = () => {
    router.push('/user-profile');
  };

  const getCurrentDate = () => {
    const today = new Date();
    return today.toLocaleDateString('en-US', { 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  return (
    <View style={styles.container}>
      <CustomStatusBar backgroundColor="transparent" barStyle="light-content" />
      
      <SafeAreaView style={styles.safeArea}>
        {/* Curved Header with Gradient */}
        <LinearGradient
          colors={['#7BA05B', '#9BC76D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.userInfoContainer}>
              <TouchableOpacity
                style={styles.profilePicture}
                onPress={handleProfilePress}
              >
                {userData.profileImage ? (
                  <Image source={userData.profileImage} style={styles.profileImage} />
                ) : (
                  <Text style={styles.profileInitial}>
                    {(userData.firstName || 'L').charAt(0)}
                  </Text>
                )}
              </TouchableOpacity>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>
                  {userData.hidePersonalInfo ? '2021*******' : (userData.controlNumber || '2021*******')}
                </Text>
              </View>
            </View>
            <TouchableOpacity style={styles.notificationButton}>
              <View style={styles.notificationIcon}>
                <Text style={styles.notificationText}>🔔</Text>
                <View style={styles.notificationBadge} />
              </View>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        <ScrollView 
          contentContainerStyle={styles.scrollContent} 
          showsVerticalScrollIndicator={false}
        >
          {/* Mood Tracker Card */}
          <Animated.View
            style={[
              styles.moodCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.moodCardContent}>
              <Text style={styles.moodTitle}>How are you feeling today?</Text>
              <View style={styles.moodDateContainer}>
                <Text style={styles.moodSubtitle}>Today's mood</Text>
                <Text style={styles.moodDate}>{getCurrentDate()}</Text>
              </View>
              
              <View style={styles.moodOptions}>
                {moodOptions.map((mood, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.moodOption,
                      selectedMood === index && styles.selectedMoodOption
                    ]}
                    onPress={() => handleMoodSelect(index)}
                  >
                    <View style={[
                      styles.moodEmojiContainer,
                      selectedMood === index && styles.selectedMoodEmojiContainer
                    ]}>
                      <Text style={styles.moodEmoji}>{mood.emoji}</Text>
                    </View>
                    <Text style={[
                      styles.moodLabel,
                      selectedMood === index && styles.selectedMoodLabel
                    ]}>
                      {mood.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              <TouchableOpacity 
                style={[
                  styles.saveMoodButton,
                  selectedMood === null && styles.saveMoodButtonDisabled
                ]}
                onPress={handleSaveMood}
                disabled={selectedMood === null}
              >
                <Text style={styles.saveMoodButtonText}>Save Mood</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Daily Insights Card */}
          {showDailyInsights && (
            <Animated.View
              style={[
                styles.insightsCard,
                {
                  opacity: fadeAnim,
                  transform: [{ translateY: slideUpAnim }]
                }
              ]}
            >
              <View style={styles.insightsHeader}>
                <Text style={styles.insightsTitle}>Daily Insights</Text>
                <TouchableOpacity 
                  style={styles.closeButton}
                  onPress={() => setShowDailyInsights(false)}
                >
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.insightsText}>
                It seems like you are stress in the past few weeks.{'\n'}
                Here are some recommendations:
              </Text>
              <View style={styles.recommendationsList}>
                <Text style={styles.recommendationItem}>• Take a deept breath</Text>
                <Text style={styles.recommendationItem}>• Write down what is bothering you</Text>
                <Text style={styles.recommendationItem}>• Do something kind to yourself</Text>
              </View>
            </Animated.View>
          )}

          {/* Upcoming Appointment Card */}
          <Animated.View
            style={[
              styles.appointmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <Text style={styles.appointmentTitle}>Upcoming Appointment</Text>
            <View style={styles.appointmentContent}>
              <View style={styles.doctorInfo}>
                <View style={styles.doctorAvatar}>
                  <Text style={styles.doctorInitial}>{upcomingAppointment.initial}</Text>
                </View>
                <View style={styles.doctorDetails}>
                  <Text style={styles.doctorName}>{upcomingAppointment.doctor}</Text>
                  <Text style={styles.doctorSpecialty}>{upcomingAppointment.specialty}</Text>
                </View>
              </View>
              <View style={styles.appointmentTime}>
                <Text style={styles.appointmentDate}>{upcomingAppointment.date}</Text>
                <Text style={styles.appointmentTimeText}>{upcomingAppointment.time}</Text>
              </View>
            </View>
            <TouchableOpacity 
              style={styles.rescheduleButton}
              onPress={() => router.push('/reschedule-appointment')}
            >
              <Text style={styles.rescheduleButtonText}>Reschedule</Text>
            </TouchableOpacity>
          </Animated.View>

          {/* AI Mental Health Assistant Card */}
          <Animated.View
            style={[
              styles.aiAssistantCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.aiAssistantContent}>
              <View style={styles.aiAssistantHeader}>
                <View style={styles.aiIcon}>
                  <Text style={styles.aiIconText}>💬</Text>
                </View>
                <Text style={styles.aiAssistantTitle}>AI Mental Health Assistant</Text>
              </View>
              <Text style={styles.aiAssistantSubtitle}>
                Feeling overwhelmed or need someone to talk to?
              </Text>
              <Text style={styles.aiAssistantDescription}>
                Our AI assistant is here to listen, provide support, and offer guidance 24/7
              </Text>
              <TouchableOpacity
                style={styles.chatButton}
                onPress={() => router.push('/ai-chatbot')}
              >
                <Text style={styles.chatButtonText}>Chat with Aira</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>

          {/* Mental Health Assessment Card */}
          <Animated.View
            style={[
              styles.assessmentCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <LinearGradient
              colors={['#7BA05B', '#9BC76D']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.assessmentGradient}
            >
              <Text style={styles.assessmentTitle}>Mental Health Assessment</Text>
              <Text style={styles.assessmentDescription}>
                Take a quuick assessment to better understand your current mental health state and get personalized recommendation
              </Text>
              <TouchableOpacity
                style={styles.startAssessmentButton}
                onPress={() => router.push('/mental-assessment')}
              >
                <Text style={styles.startAssessmentButtonText}>Start self-assessment</Text>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>

          {/* Stress Management Card */}
          <Animated.View
            style={[
              styles.stressManagementCard,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideUpAnim }]
              }
            ]}
          >
            <View style={styles.stressManagementContent}>
              <View style={styles.stressManagementHeader}>
                <View style={styles.stressIcon}>
                  <Text style={styles.stressIconText}>🧘</Text>
                </View>
                <Text style={styles.stressManagementTitle}>Stress Management</Text>
              </View>
              <Text style={styles.stressManagementDescription}>
                Track your thoughts, feelings, and activities to identify patterns and improve your mental well-being.
              </Text>
              <TouchableOpacity
                style={styles.startJournalingButton}
                onPress={() => router.push('/mood-journal')}
              >
                <Text style={styles.startJournalingButtonText}>Start journaling</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Bottom Navigation */}
        <BottomNavigation />
      </SafeAreaView>
    </View>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  safeArea: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: Platform.OS === 'ios' ? 50 : StatusBar.currentHeight + 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  profilePicture: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  profileInitial: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  notificationButton: {
    padding: 8,
  },
  notificationIcon: {
    position: 'relative',
  },
  notificationText: {
    fontSize: 24,
    color: '#FFFFFF',
  },
  notificationBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FF4444',
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  moodCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  moodCardContent: {
    padding: 20,
  },
  moodTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  moodDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  moodSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  moodDate: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  moodOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 25,
  },
  moodOption: {
    alignItems: 'center',
    flex: 1,
  },
  selectedMoodOption: {
    transform: [{ scale: 1.05 }],
  },
  moodEmojiContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedMoodEmojiContainer: {
    backgroundColor: '#7BA05B',
  },
  moodEmoji: {
    fontSize: 24,
  },
  moodLabel: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '500',
  },
  selectedMoodLabel: {
    color: '#7BA05B',
    fontWeight: 'bold',
  },
  saveMoodButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 15,
    alignItems: 'center',
  },
  saveMoodButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  saveMoodButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  insightsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  insightsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#999999',
  },
  insightsText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 15,
  },
  recommendationsList: {
    paddingLeft: 10,
  },
  recommendationItem: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 22,
    marginBottom: 5,
  },
  appointmentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  appointmentTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 15,
  },
  appointmentContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  doctorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#D4A574',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  doctorInitial: {
    fontSize: 20,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 2,
  },
  doctorSpecialty: {
    fontSize: 14,
    color: '#666666',
  },
  appointmentTime: {
    alignItems: 'flex-end',
  },
  appointmentDate: {
    fontSize: 14,
    color: '#333333',
    fontWeight: '500',
    marginBottom: 2,
  },
  appointmentTimeText: {
    fontSize: 14,
    color: '#666666',
  },
  rescheduleButton: {
    backgroundColor: '#9BC76D',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  rescheduleButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  aiAssistantCard: {
    backgroundColor: '#F0F8FF',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  aiAssistantContent: {
    padding: 20,
  },
  aiAssistantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  aiIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  aiIconText: {
    fontSize: 20,
  },
  aiAssistantTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  aiAssistantSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  aiAssistantDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 20,
  },
  chatButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  chatButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  assessmentCard: {
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  assessmentGradient: {
    padding: 20,
    borderRadius: 20,
  },
  assessmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  assessmentDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
    marginBottom: 20,
    opacity: 0.9,
  },
  startAssessmentButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  startAssessmentButtonText: {
    color: '#7BA05B',
    fontSize: 16,
    fontWeight: '600',
  },
  stressManagementCard: {
    backgroundColor: '#F8FFF8',
    borderRadius: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  stressManagementContent: {
    padding: 20,
  },
  stressManagementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  stressIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  stressIconText: {
    fontSize: 20,
  },
  stressManagementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  stressManagementDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 20,
  },
  startJournalingButton: {
    backgroundColor: '#7BA05B',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
  },
  startJournalingButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
